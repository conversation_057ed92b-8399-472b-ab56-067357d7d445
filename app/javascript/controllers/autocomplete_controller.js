import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
    static targets = ["input", "dropdown", "option"]
    static values = { 
        options: Array,
        placeholder: String
    }

    connect() {
        this.originalSelect = this.element
        this.selectedValue = this.originalSelect.value
        this.selectedText = this.getSelectedText()
        
        this.createCustomSelect()
        this.hideOriginalSelect()
    }

    disconnect() {
        if (this.customContainer) {
            this.customContainer.remove()
        }
        this.originalSelect.style.display = 'block'
    }

    createCustomSelect() {
        // Vytvoření kontejneru pro custom select
        this.customContainer = document.createElement('div')
        this.customContainer.className = 'relative w-full'
        
        // Vytvoření input pole pro vyhledávání
        this.searchInput = document.createElement('input')
        this.searchInput.type = 'text'
        this.searchInput.className = 'input w-full'
        this.searchInput.placeholder = this.placeholderValue || 'Začněte psát pro vyhledání...'
        this.searchInput.value = this.selectedText
        this.searchInput.addEventListener('input', this.handleInput.bind(this))
        this.searchInput.addEventListener('focus', this.showDropdown.bind(this))
        this.searchInput.addEventListener('blur', this.handleBlur.bind(this))
        this.searchInput.addEventListener('keydown', this.handleKeydown.bind(this))

        // Vytvoření dropdown menu
        this.dropdown = document.createElement('div')
        this.dropdown.className = 'absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto hidden'
        this.dropdown.style.top = '100%'

        this.customContainer.appendChild(this.searchInput)
        this.customContainer.appendChild(this.dropdown)
        
        // Vložení custom selectu za původní select
        this.originalSelect.parentNode.insertBefore(this.customContainer, this.originalSelect.nextSibling)
        
        this.populateDropdown()
        this.selectedIndex = -1
    }

    hideOriginalSelect() {
        this.originalSelect.style.display = 'none'
    }

    getSelectedText() {
        const selectedOption = this.originalSelect.querySelector(`option[value="${this.originalSelect.value}"]`)
        return selectedOption ? selectedOption.textContent : ''
    }

    populateDropdown() {
        this.dropdown.innerHTML = ''
        
        // Získání všech options z původního selectu
        const options = Array.from(this.originalSelect.querySelectorAll('option'))
        
        options.forEach((option, index) => {
            if (option.value === '') return // Přeskočit prázdné hodnoty
            
            const dropdownOption = document.createElement('div')
            dropdownOption.className = 'px-3 py-2 cursor-pointer hover:bg-gray-100'
            dropdownOption.textContent = option.textContent
            dropdownOption.dataset.value = option.value
            dropdownOption.dataset.index = index
            dropdownOption.addEventListener('mousedown', this.selectOption.bind(this))
            
            this.dropdown.appendChild(dropdownOption)
        })
    }

    handleInput(event) {
        const query = event.target.value.toLowerCase()
        this.filterOptions(query)
        this.showDropdown()
        this.selectedIndex = -1
    }

    filterOptions(query) {
        const options = this.dropdown.querySelectorAll('div')
        let visibleCount = 0
        
        options.forEach(option => {
            const text = option.textContent.toLowerCase()
            if (text.includes(query)) {
                option.style.display = 'block'
                visibleCount++
            } else {
                option.style.display = 'none'
            }
        })
        
        // Skrýt dropdown pokud nejsou žádné výsledky
        if (visibleCount === 0 && query.length > 0) {
            this.showNoResults()
        } else {
            this.hideNoResults()
        }
    }

    showNoResults() {
        this.hideNoResults()
        const noResults = document.createElement('div')
        noResults.className = 'px-3 py-2 text-gray-500 italic'
        noResults.textContent = 'Žádné výsledky'
        noResults.dataset.noResults = 'true'
        this.dropdown.appendChild(noResults)
    }

    hideNoResults() {
        const noResults = this.dropdown.querySelector('[data-no-results="true"]')
        if (noResults) {
            noResults.remove()
        }
    }

    showDropdown() {
        this.dropdown.classList.remove('hidden')
    }

    hideDropdown() {
        this.dropdown.classList.add('hidden')
        this.selectedIndex = -1
    }

    handleBlur(event) {
        // Zpoždění pro umožnění kliknutí na option
        setTimeout(() => {
            this.hideDropdown()
        }, 150)
    }

    handleKeydown(event) {
        const visibleOptions = Array.from(this.dropdown.querySelectorAll('div:not([style*="display: none"]):not([data-no-results])'))
        
        switch(event.key) {
            case 'ArrowDown':
                event.preventDefault()
                this.selectedIndex = Math.min(this.selectedIndex + 1, visibleOptions.length - 1)
                this.highlightOption(visibleOptions)
                break
            case 'ArrowUp':
                event.preventDefault()
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1)
                this.highlightOption(visibleOptions)
                break
            case 'Enter':
                event.preventDefault()
                if (this.selectedIndex >= 0 && visibleOptions[this.selectedIndex]) {
                    this.selectOptionByElement(visibleOptions[this.selectedIndex])
                }
                break
            case 'Escape':
                this.hideDropdown()
                this.searchInput.blur()
                break
        }
    }

    highlightOption(visibleOptions) {
        // Odstranit předchozí zvýraznění
        visibleOptions.forEach(option => {
            option.classList.remove('bg-blue-100')
        })
        
        // Zvýraznit aktuální option
        if (this.selectedIndex >= 0 && visibleOptions[this.selectedIndex]) {
            visibleOptions[this.selectedIndex].classList.add('bg-blue-100')
        }
    }

    selectOption(event) {
        this.selectOptionByElement(event.target)
    }

    selectOptionByElement(optionElement) {
        const value = optionElement.dataset.value
        const text = optionElement.textContent
        
        // Nastavit hodnotu do původního selectu
        this.originalSelect.value = value
        
        // Nastavit text do input pole
        this.searchInput.value = text
        
        // Skrýt dropdown
        this.hideDropdown()
        
        // Trigger change event na původním selectu
        this.originalSelect.dispatchEvent(new Event('change', { bubbles: true }))
    }
}
