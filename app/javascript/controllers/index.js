// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a show controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application"
import 'swiper/css/bundle'

import NavController from "./nav_controller"
import SubscriptionsController from "./subscriptions_controller"
import CartSnippetController from './cart_snippet_controller'
import ToggleController from './toggle_controller'
import AutocompleteController from './autocomplete_controller'
import Carousel from 'stimulus-carousel'
import { Slideover, Tabs, Modal } from "tailwindcss-stimulus-components"
import ArticleEditorController from '../controllers/article_editor_controller'

application.register('slideover', Slideover)
application.register("nav", NavController)
application.register("subscriptions", SubscriptionsController)
application.register('cartSnippet', CartSnippetController)
application.register('toggle', ToggleController)
application.register('autocomplete', AutocompleteController)
application.register('carousel', Carousel)
application.register('tabs', Tabs)
application.register('modal', Modal)

application.register('article-editor', ArticleEditorController)
