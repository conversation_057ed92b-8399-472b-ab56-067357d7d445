
<a href="<%= admin_articles_path %>" class="bg-gray-200 hover:bg-gray-300 p-2 text-gray-600 inline-flex space-x-2">
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
    <path stroke-linecap="round" stroke-linejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
  </svg>

  <span>Zpět</span>
</a>

<style>
  .field_with_errors input, .field_with_errors textarea  {
      background: #fff8f8;
  }
</style>

<div class="max-w-4xl mx-auto bg-white border border-gray-200 shadow py-4 px-6">
  <h5 class="text-xl font-medium">
    <% if article.persisted? %>
      Upravit <PERSON> <%= article.title %>
    <% else %>
      P<PERSON>ida<PERSON> nov<PERSON>
    <% end %>
  </h5>

  <div class="mt-4">
    <%= form_with model: [:admin, article] do |f| %>
      <% if article.errors.any? %>
        <div class="alert bg-red-50 p-2 mb-5 border border-red-200 w-full">
          Nepodařilo se vytvořit produkt protože:
          <ul class="list-disc list-inside">
            <% article.errors.each do |error| %>
              <li class="ml-4"><%= error.full_message %></li>
            <% end %>
          </ul>
        </div>
        <% end %>

      <div>
        <label for="year" class="block text-sm font-medium text-gray-700">Název článku</label>
        <div class="mt-1">
          <%= f.text_field :title, class: "input w-full" %>
        </div>
      </div>

      <div class="mt-4">
        <label for="year" class="block text-sm font-medium text-gray-700">Datum publikace</label>
        <div class="mt-2">
          <%= f.datetime_field :published_at, class: "input" %>
        </div>
      </div>

      <div class="mt-4">
        <label for="year" class="block text-sm font-medium text-gray-700">Perex</label>
        <div class="mt-2">
          <%= f.text_area :perex, class: "input" %>
        </div>
      </div>

      <div class="w-full mt-3">
        <label class="text-sm">
          Autor
        </label>
        <div class="w-full mt-1">
          <%= f.select :author_id, [['-- vyberte autora --', '']] + Author.ordered_by_surname.map { |r| [r.name, r.id] }, {}, { class: "input", data: { controller: "autocomplete", autocomplete_placeholder_value: "Začněte psát jméno autora..." } } %>
        </div>
      </div>

      <div class="w-full mt-3">
        <label class="text-sm">
          Z čísla časopisu PAPOUŠCI
        </label>
        <div class="w-full mt-1">
          <%= f.select :magazine_id, [['-- není z časopisu --', nil]] + Magazine.all.map { |r| [r.issue, r.id] }, {}, { class: "input", data: { controller: "autocomplete" } } %>
        </div>
      </div>

      <div class="w-full mt-3">
        <label class="text-sm">
          Ostatní autoři
        </label>
        <div class="w-full mt-1">
          <%= f.text_field :other_authors, class: "input" %>
        </div>
      </div>

      <div class="w-full mt-3">
        <label class="text-sm">
          Druh papouška
        </label>
        <div class="w-full mt-1">
          <%= f.text_field :species, class: "input" %>
        </div>
      </div>

      <div class="mt-4">
        <label for="content" class="block text-sm font-medium text-gray-700">Obsah</label>
        <div class="mt-2">
          <%= f.text_area :content, data: { controller: "article-editor" } %>
        </div>
      </div>

      <div class="mt-3">
        <label class="block text-sm font-medium text-gray-700">Zařazení</label>
        <%= f.collection_check_boxes(:category_ids, Article::Category.all, :id, :name) do |category| %>
          <div class="flex space-x-2">
            <div><%= category.check_box(class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600") %></div>
            <div class="pt-1" style="vertical-align: middle;"><%= category.label %></div>
          </div>
        <% end %>
      </div>

      <div class="mt-3">
        <div class="flex space-x-2">
          <div><%= f.check_box :display_ads %></div>
          <div class="pt-1" style="vertical-align: middle;">Zobrazovat reklamy</div>
        </div>
      </div>

      <div class="mt-4">
        <label for="content" class="block text-sm font-medium text-gray-700">Náhledové foto (1260 × 800px)</label>
        <div class="mt-2 flex items-center space-x-2">
          <% if article.cover.representable? %>
            <%= image_tag article.cover.variant(resize_to_fill: [120, 80])  %>
          <% end %>

          <%= f.file_field :cover %>
        </div>
      </div>

      <div class="mt-4">
        <label for="content" class="block text-sm font-medium text-gray-700">Foto FACEBOOK (1200 × 630px)</label>
        <div class="mt-2 flex items-center space-x-2">
          <% if article.banner.representable? %>
            <%= image_tag article.banner.variant(resize_to_fill: [120, 60])  %>
          <% end %>

          <%= f.file_field :banner %>
        </div>
      </div>

      <div class="my-4">
        <%= f.submit "Uložit článek", class: "bg-rose-500 hover:bg-rose-600 cursor-pointer rounded text-white p-2" %>
      </div>
  <% end %>
  </div>
</div>
