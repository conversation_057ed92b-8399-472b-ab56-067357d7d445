<% title @article.title %>
<% description @article.perex %>

<% set_meta_tags og: {
  title: @article.title,
  type: "article",
  url: article_url(@article),
} %>

<% set_meta_tags og: {
  image: cdn_image_url(@article.banner)
} if @article.banner.attached? %>

<link rel="stylesheet" href="/article-editor/css/arx-content.css">

<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6 lg:py-12">
  <div class="mx-auto max-w-5xl">
    <nav class="flex mb-4" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-2">
        <li>
          <div>
            <a href="<%= articles_path %>" class="text-sm text-gray-500 hover:text-gray-700">
              <PERSON><PERSON><PERSON><PERSON>
            </a>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
            </svg>
            <a href="<%= articles_path(@article.categories.first.slug) %>" class="ml-2 text-sm text-gray-500 hover:text-gray-700">
              <%= @article.categories.first.name %>
            </a>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
            </svg>
            <span class="ml-2 text-sm text-gray-500 hover:text-gray-700" aria-current="page">
              <%= @article.title %>
            </span>
          </div>
        </li>
      </ol>
    </nav>

    <h1 class="text-2xl font-medium"><%= @article.title %></h1>
    <p class="text-sm text-gray-600">Autor: <%= @article.author.name %></p>

    <% if user_signed_in? && current_user&.author == @article.author %>
      <a href="<%= edit_article_path(@article) %>" class="button">Upravit článek</a>
    <% end %>

    <% if @article.other_authors.present? %>
      <p class="text-sm text-gray-600">Ostatní autoři: <%= @article.other_authors %></p>
    <% end %>

    <div class="fb-share-button mt-3"
         data-href="<%= article_url(@article) %>"
         data-layout="button_count">
    </div>

    <div class="flex flex-col lg:flex-row lg:space-x-8">
      <div class="max-w-3xl">
        <div class="py-4 article-html">
          <%== @article.content %>
        </div>

        <% if @recommended_products.present? %>
          <h4 class="font-medium text-xl">Chcete se dozvědět více?</h4>

          <div class="my-4">
            <div class="-mx-px grid grid-cols-2 border-l border-gray-200 sm:mx-0 md:grid-cols-3 lg:grid-cols-3 border-t border-gray-200">
              <% @recommended_products.each do |product| %>
                <%= render "category/product", product: %>
              <% end %>
            </div>
          </div>
      <% end %>
      </div>
      <div>
        <h4 class="text-articles-500 text-xl font-medium mb-2">Rubriky</h4>
        <div class="px-2 py-1 bg-white">
          <%= render partial: "categories" %>
        </div>
      </div>
    </div>

  </div>
</div>


