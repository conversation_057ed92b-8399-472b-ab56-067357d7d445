<style>
  .field_with_errors input, .field_with_errors textarea  {
      background: #fcd8d8;
      border-color: #f16767;
  }
</style>

<div class="max-w-4xl mx-auto bg-white border border-gray-200 shadow py-4 px-6 my-4">
  <h5 class="text-xl font-medium flex justify-between">
    <% if article.persisted? %>
      Upravit <PERSON> <%= article.title %>
      <a href="<%= article_path(article) %>" target="_blank" class="underline">Zobrazit článek</a>
    <% else %>
      Přidat nový článek
    <% end %>
  </h5>

  <div class="mt-4">
    <%= form_with model: article, url: article.persisted? ? update_article_path(article) : create_article_path do |f| %>
      <% if article.errors.any? %>
        <div class="alert bg-red-50 p-2 mb-5 border border-red-200 w-full">
          Zkontrolujte prosím formulář. Červeně označené pole obsahují chybu.
        </div>
        <% end %>

      <div>
        <label for="year" class="block text-sm font-medium text-gray-700">Název článku <small class="text-red-500">*povinné pole</small></label>
        <div class="mt-1">
          <%= f.text_field :title, class: "input w-full" %>
        </div>
      </div>

      <div class="mt-4">
        <label for="year" class="block text-sm font-medium text-gray-700">Datum a čas publikace <small class="text-red-500">*povinné pole</small></label>
        <div class="mt-2">
          <%= f.datetime_field :published_at, class: "input" %>
        </div>
      </div>

      <div class="mt-4">
        <label for="year" class="block text-sm font-medium text-gray-700">Perex - krátký úvodní text,  <small class="text-red-500">*povinné pole</small></label>
        <div class="mt-2">
          <%= f.text_area :perex, class: "input" %>
        </div>
        <p class="mt-1 text-sm text-gray-500">
          Ideální délka 80 znaků
        </p>
        <p class="mt-1 text-sm text-gray-500">
          Zobrazuje se u výpisu článků
        </p>
      </div>

      <div class="w-full mt-3">
        <label class="text-sm">
          Ostatní autoři
        </label>
        <div class="w-full mt-1">
          <%= f.text_field :other_authors, class: "input" %>
        </div>
        <p class="mt-1 text-sm text-gray-500">
          Vyplňte celá jména autorů oddělená čárkou v případě že se na článku podíleli i jiní autoři.
        </p>
      </div>

      <div class="w-full mt-3">
        <label class="text-sm">
          Druh papouška
        </label>
        <div class="w-full mt-1">
          <%= f.text_field :species, class: "input" %>
        </div>
        <p class="mt-1 text-sm text-gray-500">
          Vyplňte v případě, že se článek týká konkrétního druhu papouška.
        </p>
      </div>

      <div class="mt-4">
        <label for="content" class="block text-sm font-medium text-gray-700">Obsah <small class="text-red-500">*povinné pole</small></label>
        <div class="mt-2">
          <%= f.text_area :content, data: { controller: "article-editor" } %>
        </div>
      </div>

      <div class="mt-3">
        <label class="block text-sm font-medium text-gray-700">Zařazení <small class="text-red-500">*povinné pole</small></label>
        <%= f.collection_check_boxes(:category_ids, Article::Category.all, :id, :name) do |category| %>
          <div class="flex space-x-2">
            <div><%= category.check_box(class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600") %></div>
            <div class="pt-1" style="vertical-align: middle;"><%= category.label %></div>
          </div>
        <% end %>
      </div>

      <div class="mt-4">
        <label for="content" class="block text-sm font-medium text-gray-700">Náhledové foto (širokoúhlé)</label>
        <div class="mt-2 flex items-center space-x-2">
          <% if article.cover.representable? %>
            <%= image_tag article.cover.variant(resize_to_fill: [50, 50])  %>
          <% end %>

          <%= f.file_field :cover %>
        </div>
      </div>

      <div class="mt-4">
        <label for="content" class="block text-sm font-medium text-gray-700">Foto FACEBOOK (1200x630px)</label>
        <div class="mt-2 flex items-center space-x-2">
          <% if article.banner.representable? %>
            <%= image_tag article.banner.variant(resize_to_fill: [120, 63])  %>
          <% end %>

          <%= f.file_field :banner %>
        </div>
      </div>

      <hr class="my-4">
      <div class="mt-4">
        <%= f.submit "Uložit článek", class: "bg-rose-500 hover:bg-rose-600 cursor-pointer rounded text-white p-2" %>
      </div>
  <% end %>
  </div>
</div>
